@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Neonderthaw&display=swap');

:root {
  /* === MONOCHROMATIC PURPLE PALETTE === */
  --primary: #7C2D92;          /* Deep Purple - Main brand color */
  --secondary: #9333EA;        /* Medium Purple - Interactive elements */
  --accent: #A855F7;           /* Bright Purple - Highlights & accents */
  --dark-base: #1A0B2E;        /* Very Dark Purple - Background base */

  /* === GRADIENT BACKGROUNDS === */
  --gradient-main: linear-gradient(135deg, #1A0B2E 0%, #2D1B4E 30%, #44246A 70%, #5B2C87 100%);
  --gradient-card: linear-gradient(135deg, rgba(124, 45, 146, 0.15) 0%, rgba(147, 51, 234, 0.08) 100%);
  --gradient-hover: linear-gradient(135deg, rgba(124, 45, 146, 0.25) 0%, rgba(147, 51, 234, 0.15) 100%);

  /* === GLASSMORPHISM EFFECTS === */
  --glass-bg: rgba(147, 51, 234, 0.12);
  --glass-border: rgba(168, 85, 247, 0.25);
  --glass-shadow: 0 8px 32px rgba(26, 11, 46, 0.6);
  --glass-blur: blur(20px);

  /* === TEXT & OUTLINE === */
  --text-primary: #FFFFFF;
  --text-secondary: #E1D5F0;    /* Light purple-tinted white */
  --text-muted: #B794C7;        /* Muted purple */
  --outline-stroke: #A855F7;
  --outline-glow: 0 0 20px rgba(168, 85, 247, 0.4);

  /* === INTERACTIVE STATES === */
  --hover-primary: #8B5CF6;
  --hover-secondary: #A855F7;
  --focus-ring: rgba(168, 85, 247, 0.6);

  /* === SEMANTIC COLORS === */
  --success: #22C55E;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: var(--accent);

  /* === SPACING & SIZING === */
  --border-radius: 16px;
  --border-radius-sm: 8px;
  --border-radius-lg: 24px;

  /* === SHADOWS === */
  --shadow-sm: 0 2px 8px rgba(26, 11, 46, 0.3);
  --shadow-md: 0 4px 16px rgba(26, 11, 46, 0.4);
  --shadow-lg: 0 8px 32px rgba(26, 11, 46, 0.5);
  --shadow-glow: 0 0 40px rgba(124, 45, 146, 0.4);

  /* === ADDITIONAL PURPLE SHADES FOR VARIETY === */
  --purple-50: #F3E8FF;        /* Very light purple for rare light elements */
  --purple-100: #E9D5FF;       /* Light purple */
  --purple-200: #DDD6FE;       /* Lighter purple */
  --purple-300: #C4B5FD;       /* Medium-light purple */
  --purple-400: #A78BFA;       /* Medium purple */
  --purple-500: #8B5CF6;       /* Standard purple */
  --purple-600: #7C3AED;       /* Darker purple */
  --purple-700: #6D28D9;       /* Deep purple */
  --purple-800: #5B21B6;       /* Very deep purple */
  --purple-900: #4C1D95;       /* Darkest purple */
  --purple-950: #2E1065;       /* Ultra dark purple */
}

/* === UTILITY CLASSES === */

/* Main background */
.bg-main {
  background: var(--gradient-main);
  min-height: 100vh;
}

/* Alternative darker background */
.bg-dark {
  background: radial-gradient(ellipse at center, #2D1B4E 0%, #1A0B2E 70%);
  min-height: 100vh;
}

/* Glassmorphism card */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  box-shadow: var(--glass-shadow);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: var(--gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--shadow-glow);
}

/* Subtle card variant for less emphasis */
.glass-card-subtle {
  background: rgba(91, 44, 135, 0.08);
  backdrop-filter: var(--glass-blur);
  border: 1px solid rgba(168, 85, 247, 0.15);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 16px rgba(26, 11, 46, 0.3);
  transition: all 0.3s ease;
}

/* Outline text styles */
.text-outline {
  -webkit-text-stroke: 2px var(--outline-stroke);
  -webkit-text-fill-color: transparent;
  font-weight: 700;
  text-shadow: var(--outline-glow);
}

.text-outline-filled {
  -webkit-text-stroke: 1px var(--outline-stroke);
  -webkit-text-fill-color: var(--text-primary);
  font-weight: 600;
}

/* Button styles */
.btn-primary {
  background: var(--gradient-card);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  padding: 12px 24px;
  border-radius: var(--border-radius-sm);
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-primary:hover {
  background: var(--hover-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:focus {
  outline: 2px solid var(--focus-ring);
  outline-offset: 2px;
}

/* Accent button for important actions */
.btn-accent {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border: none;
  color: var(--text-primary);
  padding: 12px 24px;
  border-radius: var(--border-radius-sm);
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(124, 45, 146, 0.3);
}

.btn-accent:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(124, 45, 146, 0.4);
}

/* Text colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-accent { color: var(--accent); }

/* Gradient text */
.text-gradient {
  background: linear-gradient(135deg, var(--secondary), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Alternative gradient for variety */
.text-gradient-alt {
  background: linear-gradient(135deg, var(--primary), var(--purple-400));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Section dividers */
.section-divider {
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent), transparent);
  border: none;
  margin: 2rem 0;
}

/* Glow effects for special elements */
.glow-purple {
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
}

.glow-purple:hover {
  box-shadow: 0 0 30px rgba(168, 85, 247, 0.5);
}

.hero{
  height: 100vh;
}
body{
  margin: 0;
  padding: 0;
  background: var(--gradient-main);
  color:var(--text-primary);
  font-family: 'montserrat';
}

#name{
  font-family: Arial, Helvetica, sans-serif;
}